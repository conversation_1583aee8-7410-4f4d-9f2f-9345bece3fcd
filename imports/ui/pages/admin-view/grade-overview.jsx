import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { withRouter } from "react-router-dom";
import { withTracker } from "meteor/react-meteor-data";
import { chunk, flatten } from "lodash";
import { But<PERSON> } from "react-bootstrap";

import { Loading } from "../../components/loading";
import Header from "../../components/admin-view/admin-header";
import GradeLevelScreeningInProgress from "../../components/admin-view/admin-screening-grade-level-progress";

import ClasswideQuickInfoSection from "../../components/admin-view/classwide-quick-info-section";
import IndividualQuickInfoSection from "../../components/admin-view/individual-quick-info-section";
import { getCurrentEnrolledGrade } from "/imports/api/students/utils";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Students } from "/imports/api/students/students";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import IndividualOverviewItem from "./individual-overview-item.jsx";
import {
  getCurrentSchoolYear,
  getInActiveSchoolYear,
  getMeteorUserSync,
  ninjalog,
  sortByGradeAndName
} from "/imports/api/utilities/utilities";
import ScrollIndicator from "../../components/scrollIndicator";
import ScrollIndicatorView from "../../components/scrollIndicatorView";
import { areSubscriptionsLoading, isHighSchoolGrade } from "../../utilities";
import { HighSchoolGroupsList } from "./high-school-groups-list";
import { Assessments } from "/imports/api/assessments/assessments";
import { AssessmentGrowth } from "/imports/api/assessmentGrowth/assessmentGrowth";
import { compileResultsByStudentGroupsInGrade, compileScreeningStatusForGrade } from "./utilities";
import ClasswideInterventionTable from "./classwide-intervention-table";
import { getGrowthResults } from "/imports/api/assessmentGrowth/utilities";
import { GrowthChartWrapper } from "../student-groups/growth-chart-wrapper";
import ClasswideDetailModal from "../student-detail/classwide-detail-modal";

const renderNoGroupsMessage = groupType => (
  <h3 className="stamped">{`No students in this grade are currently practicing ${groupType} interventions.`}</h3>
);

export class GradeOverviewComponent extends Component {
  constructor(props) {
    super(props);

    this.state = {
      gradeDemographics: null,
      screeningInProgress: true,
      allClasswideStats: null,
      allIndividualStats: null,
      isDataFetching: false,
      studentGroupEnrollments: [],
      shouldShowDetailModal: false
    };
  }

  componentDidMount() {
    this.updateClasswideStats();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.grade !== this.props.grade || prevProps.siteName !== this.props.siteName) {
      this.updateClasswideStats();
    }
  }

  updateClasswideStats = () => {
    const { props } = this;
    if (props.studentGroups) {
      this.setState({ isDataFetching: true });
      const studentGroupIds = props.studentGroups.map(sg => sg._id);
      Meteor.call(
        "calculateClasswideStatsForMultipleGroups",
        studentGroupIds,
        true,
        (err, { allClasswideResults = [], studentGroupEnrollments }) => {
          if (err) {
            ninjalog.error({
              msg: `error getting classwide intervention stats: ${err}`,
              context: "admin"
            });
          } else {
            const allIndividualStats = [];
            allClasswideResults.forEach(group => {
              allIndividualStats.push({
                results: group.extendedIndividualResults.individualResults,
                interventionConsistency: group.extendedIndividualResults.interventionConsistency,
                groupId: group.studentGroupId
              });
            });
            this.setState({ allClasswideStats: allClasswideResults, studentGroupEnrollments, allIndividualStats });
          }
          this.setState({ isDataFetching: false });
        }
      );
      if (this.props.grade === "HS") {
        Meteor.call(
          "Growth:getHSClasswideInterventionProgress",
          {
            orgid: this.props.orgid,
            studentGroupIds,
            siteId: props.studentGroups[0].siteId
          },
          (err, res) => {
            if (err) {
              Alert.error(err.message);
            } else {
              this.setState({ growthStats: res });
            }
          }
        );
      }
    }
  };

  percentAtRisk() {
    const screeningResults = {
      meeting: 0,
      atRisk: 0,
      total: 0,
      percentAtRisk: 0
    };

    this.props.assessmentResults.forEach(result => {
      screeningResults.meeting = result.classwideResults.totalStudentsMeetingAllTargets + screeningResults.meeting;
      screeningResults.total = result.classwideResults.totalStudentsAssessedOnAllMeasures + screeningResults.total;
    });
    screeningResults.atRisk = screeningResults.total - screeningResults.meeting;
    screeningResults.percentAtRisk = screeningResults.atRisk / screeningResults.total;
    return screeningResults;
  }

  renderIndividualSummaryTable() {
    const individualAssessmentResults = this.props.assessmentResults.filter(ar => ar.type === "individual");
    const individualStudentRowContent = this.props.studentGroups.reduce((result, group, index) => {
      let curResult = result;
      if (!group.currentAssessmentResultIds) {
        return curResult;
      }
      const currentIndividualAssessmentResults = individualAssessmentResults.filter(ar =>
        group.currentAssessmentResultIds.includes(ar._id)
      );

      if (currentIndividualAssessmentResults.length) {
        if (!Array.isArray(curResult)) {
          curResult = [];
        }
        curResult.push(
          <IndividualOverviewItem
            key={index}
            ownerIds={group.ownerIds}
            group={group}
            ruleCountTotal={this.props.ruleSkillsCount}
            currentBMPeriod={this.props.currentBMPeriod}
            students={this.props.students}
            assessmentResults={individualAssessmentResults}
          />
        );
      }
      return curResult;
    }, renderNoGroupsMessage("individual"));
    if (Array.isArray(individualStudentRowContent)) {
      return (
        <div className="tblIntvSummaryTable">
          <div className="row rowIndvSummaryHeading">
            <div className="col-md-3">Teacher (Group)</div>
            <div className="col-md-2">Current Intervention</div>
            <div className="col-md-2">
              Most Recent <br /> Score Entry
            </div>
            <div className="col-md-2">
              Intervention <br /> Consistency
            </div>
            <div className="col-md-1">
              Average Weeks <br /> Per Skill
            </div>
            <div className="col-md-2 input-date">
              Calculations <br /> As Of Date
            </div>
          </div>
          {individualStudentRowContent}
        </div>
      );
    }
    return individualStudentRowContent;
  }

  render() {
    const {
      growthChartResults,
      loading,
      siteId,
      headerTitle,
      grade,
      gradeLevelScreeningProgress,
      currentBMPeriod,
      siteName,
      studentGroups,
      inActiveSchoolYear,
      isHighSchoolGroup,
      assessmentResults,
      ruleSkillsCount,
      orgid
    } = this.props;
    const { gradeDemographics, isDataFetching, allClasswideStats, studentGroupEnrollments } = this.state;
    if (loading) {
      return <Loading />;
    }
    return (
      <div>
        <Header keyLabel="gradeOverview" siteId={siteId} headerTitle={headerTitle} headerStats={gradeDemographics} />
        <ScrollIndicator
          container={this}
          targetSelector={".conOverviewMain"}
          indicatorComponent={<ScrollIndicatorView />}
          uniqKey={grade}
          wait={isDataFetching}
        >
          <div className="conOverviewMain animated fadeIn">
            {isHighSchoolGroup ? null : (
              <GradeLevelScreeningInProgress
                currentBMPeriod={currentBMPeriod}
                grade={grade}
                siteName={siteName}
                progressData={gradeLevelScreeningProgress}
                inActiveSchoolYear={inActiveSchoolYear}
                studentGroups={studentGroups}
                orgid={orgid}
                studentGroupEnrollments={studentGroupEnrollments}
              />
            )}
            {isDataFetching ? (
              <Loading />
            ) : (
              <div className="conOverviewContent">
                <HighSchoolGroupsList
                  shouldDisplay={isHighSchoolGroup}
                  studentGroups={studentGroups}
                  siteId={siteId}
                  grade={grade}
                />
                {inActiveSchoolYear ? (
                  <ClasswideQuickInfoSection
                    data={allClasswideStats}
                    grade={grade}
                    siteId={siteId}
                    studentGroups={studentGroups}
                    orgid={orgid}
                  />
                ) : null}
                <section>
                  <div className="d-flex flex-row justify-content-between">
                    <h2>
                      <i className="fa fa-users" /> Classwide Interventions
                    </h2>
                    <span>
                      {this.state.shouldShowDetailModal ? (
                        <ClasswideDetailModal
                          onCloseModal={() => {
                            this.setState({ shouldShowDetailModal: false });
                          }}
                          showModal={this.state.shouldShowDetailModal}
                          siteId={siteId}
                          grade={grade}
                          schoolYear={this.props.schoolYear}
                          componentContext="gradeDetail"
                        />
                      ) : (
                        <Button
                          className="btn btn-primary"
                          data-testid="gradeDetail"
                          onClick={() => {
                            this.setState({ shouldShowDetailModal: true });
                          }}
                        >
                          Skill Progress by Class
                        </Button>
                      )}
                    </span>
                  </div>
                  <ClasswideInterventionTable
                    allClasswideStats={allClasswideStats}
                    studentGroups={studentGroups}
                    assessmentResults={assessmentResults}
                    studentGroupEnrollments={studentGroupEnrollments}
                    ruleSkillsCount={ruleSkillsCount}
                    currentBMPeriod={currentBMPeriod}
                    loading={!allClasswideStats}
                  />
                </section>
                {inActiveSchoolYear ? (
                  <IndividualQuickInfoSection data={this.state.allIndividualStats} siteId={siteId} />
                ) : null}
                <section>
                  <h2>
                    <i className="fa fa-user" /> Individual Interventions
                  </h2>
                  {this.renderIndividualSummaryTable()}
                </section>
                <div className="studentDetailContent">
                  {isHighSchoolGroup ? (
                    chunk(this.state.growthStats, 4).map((statChunk, index) => {
                      return (
                        <GrowthChartWrapper
                          comparisonPeriod={"all"}
                          growthChartResults={statChunk}
                          grade="HS"
                          key={`growthStats${index}`}
                          chartId={`growth_All_HS${index}`}
                          noChartTitleAndLegend={index > 0}
                        />
                      );
                    })
                  ) : (
                    <>
                      <GrowthChartWrapper comparisonPeriod="fall" growthChartResults={growthChartResults} />
                      <GrowthChartWrapper comparisonPeriod="spring" growthChartResults={growthChartResults} />
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </ScrollIndicator>
      </div>
    );
  }
}

GradeOverviewComponent.propTypes = {
  assessmentResults: PropTypes.array,
  currentBMPeriod: PropTypes.object,
  grade: PropTypes.string,
  gradeLevelScreeningProgress: PropTypes.array,
  headerTitle: PropTypes.string,
  loading: PropTypes.bool,
  percentMeetingTargetByClassAndSeason: PropTypes.array,
  ruleSkillsCount: PropTypes.number,
  siteId: PropTypes.string,
  siteName: PropTypes.string,
  orgid: PropTypes.string,
  schoolYear: PropTypes.number,
  studentGroups: PropTypes.array,
  students: PropTypes.array,
  inActiveSchoolYear: PropTypes.bool,
  isHighSchoolGroup: PropTypes.bool,
  growthChartResults: PropTypes.object
};

const GradeOverviewComponentWithTracker = withTracker(
  ({
    grade,
    bmPeriods,
    currentBMPeriod,
    schoolYear: contextSchoolYear,
    orgid: contextOrgid,
    siteId: contextSiteId
  }) => {
    let students;
    const curUser = getMeteorUserSync();
    const orgid = contextOrgid || curUser?.profile?.orgid;
    const siteId = contextSiteId || curUser?.profile?.siteAccess?.[0]?.siteId;
    const schoolYear = contextSchoolYear || getCurrentSchoolYear(curUser, orgid);
    const headerTitle = getCurrentEnrolledGrade(grade);
    const studentGroups = StudentGroups.find({
      siteId,
      grade,
      schoolYear
    })
      .fetch()
      .sort(sortByGradeAndName);
    const studentGroupIds = studentGroups.map(sg => sg._id);
    const studentsSub = Meteor.subscribe("Students:PerGrade", { orgid, grade, schoolYear, siteId });
    const growthSub = Meteor.subscribe("AssessmentGrowth", grade);
    const assessmentSub = Meteor.subscribe("AssessmentsForGrade", grade);
    const assessmentResultsSub = Meteor.subscribe(
      "AssessmentResults:IndividualByStudentGroupIds",
      studentGroupIds,
      schoolYear,
      grade
    );
    const loading = areSubscriptionsLoading(studentsSub, growthSub, assessmentSub, assessmentResultsSub);
    if (!studentGroups || !studentGroups.length) {
      // No student groups found for this site and grade
      return { loading: true };
    }
    let growthChartResults = {};
    let assessmentResults = [];
    let percentMeetingTargetByClassAndSeason = [];
    let gradeLevelScreeningProgress = {
      grade,
      gradeGroups: []
    };

    if (!loading) {
      assessmentResults = AssessmentResults.find({
        studentGroupId: { $in: studentGroupIds }
      }).fetch();
      percentMeetingTargetByClassAndSeason = compileResultsByStudentGroupsInGrade(
        bmPeriods,
        grade,
        studentGroups,
        assessmentResults.filter(ar => ar.status === "COMPLETED" && ar.type === "benchmark")
      );
      gradeLevelScreeningProgress = compileScreeningStatusForGrade(
        currentBMPeriod,
        studentGroups,
        grade,
        assessmentResults
      );

      students = Students.find({ orgid }).fetch();

      const gradeAssessments = Assessments.find({ associatedGrades: grade }, { fields: { name: 1 } }).fetch();
      const assessmentComparisonMap = AssessmentGrowth.findOne({ grade });
      if (!isHighSchoolGrade(grade)) {
        try {
          growthChartResults = getGrowthResults({
            history: flatten(studentGroups.map(sq => sq.history || [])),
            assessmentComparisonMap,
            assessments: gradeAssessments
          });
        } catch (e) {
          const genericErrorMessage = "There was a problem while getting aggregate growth chart data";
          const growthChartErrorRegExp = new RegExp(/getGrowthResults/);
          Alert.error(growthChartErrorRegExp.test(e.message) ? e.message : genericErrorMessage, { timeout: 5000 });
        }
      }
    }
    // TODO(fmazur) - await
    const inActiveSchoolYear = getInActiveSchoolYear(schoolYear, curUser, orgid);
    return {
      assessmentResults,
      gradeLevelScreeningProgress: gradeLevelScreeningProgress.gradeGroups,
      headerTitle,
      loading,
      orgid,
      percentMeetingTargetByClassAndSeason,
      siteId,
      studentGroups,
      students,
      inActiveSchoolYear,
      growthChartResults,
      isHighSchoolGroup: isHighSchoolGrade(grade)
    };
  }
)(GradeOverviewComponent);

export default withRouter(GradeOverviewComponentWithTracker);

export { GradeOverviewComponent as PureGradeOverview, GradeOverviewComponentWithTracker }; // for testing
