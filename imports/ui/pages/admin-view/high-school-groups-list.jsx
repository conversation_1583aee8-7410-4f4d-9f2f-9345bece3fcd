import React, { Component } from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import { getOwnerName } from "./classwide-overview-item";

export class HighSchoolGroupsList extends Component {
  render() {
    const displayGrade = this.props.grade[0] === "0" ? this.props.grade.slice(1) : this.props.grade;
    const suffix = `in ${displayGrade}th Grade:`;
    return this.props.shouldDisplay ? (
      <section>
        <h2>Student Groups {this.props.grade === "HS" ? "" : suffix}</h2>
        <ul>
          {this.props.studentGroups.map(({ _id, name, ownerIds, orgid: orgId }) => (
            <li key={_id}>
              <Link to={`/${orgId}/site/${this.props.siteId}/student-groups/${_id}/`}>
                {ownerIds ? `${getOwnerName(ownerIds)} (${name})` : name}
              </Link>
            </li>
          ))}
        </ul>
      </section>
    ) : null;
  }
}

HighSchoolGroupsList.propTypes = {
  shouldDisplay: PropTypes.bool,
  studentGroups: PropTypes.array,
  siteId: PropTypes.string,
  grade: PropTypes.string
};
