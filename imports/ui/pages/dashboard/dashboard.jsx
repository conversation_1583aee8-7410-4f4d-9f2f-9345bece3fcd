import moment from "moment";
import React, { useEffect, useContext, useState } from "react";
import { useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import get from "lodash/get";

import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { isEmpty } from "lodash";
import { InterventionWithoutScreening, Screening } from "../screening";
import MessageNotice from "../../components/message-notices/message-notice.jsx";
import * as messageNoticeUtils from "/imports/api/messageNotices/methods";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { Loading } from "../../components/loading.jsx";
import * as utils from "/imports/api/utilities/utilities";
import { getBenchmarkPeriodByDate, getNextBMPeriod } from "/imports/api/benchmarkPeriods/methods";
import ScreeningWindowNotice from "../../components/screening/screening-window-notice.jsx";
import ScreeningWindowStarted from "../../components/screening/screening-window-started.jsx";
import DashboardClasswide from "../../components/dashboard/dashboard-classwide.jsx";
import DashboardIndividual from "../../components/dashboard/dashboard-individual.jsx";
import Classroom from "../../pages/classroom/classroom";
import ClasswideDetail from "../student-detail/classwide-detail";
import Growth from "../student-groups/growth";
import { isSupportUser } from "/imports/api/roles/methods";
import { areSubscriptionsLoading, isHighSchoolGrade, isOnPrintPage } from "../../utilities";
import { ScoreEntryProvider } from "../../components/score-entry/score-entry-context";
import { startNewClasswide } from "../screening/screening";
import { DashboardNav } from "../../components/dashboard/dashboard-nav";
import { getMeteorUserId, getMeteorUserSync } from "/imports/api/utilities/utilities";
import { AppDataContext } from "../../routing/AppDataContext";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";
import { StaticDataContext } from "../../../contexts/StaticDataContext";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";

// flags because they might be in many situations at once
// and we need to avoid conditional-hell
const { situations } = utils;
const navs = utils.dashboardNavs;

function Dashboard(props) {
  const context = useContext(AppDataContext);

  useEffect(() => {
    return () => {
      messageNoticeUtils.clearLocationMessages("side-nav-layout");
      if (context.activeNavName) {
        context.setContext({
          activeNavName: null
        });
      }
    };
  }, [context]);

  const getGrowthPage = () => {
    return {
      component: (
        <Growth
          studentGroup={props.studentGroup}
          inActiveSchoolYear={props.inActiveSchoolYear}
          isReadOnly={props.isReadOnly}
          schoolYear={props.schoolYear}
        />
      ),
      navName: navs.growth
    };
  };

  const getIndividualInterventionPage = () => {
    return {
      component: (
        <DashboardIndividual
          studentGroup={props.studentGroup}
          inActiveSchoolYear={props.inActiveSchoolYear}
          isReadOnly={props.isReadOnly}
          students={props.students}
          assessmentResults={props.currentAssessmentResults.filter(ar => ar.type === "individual")}
        />
      ),
      navName: navs.individual
    };
  };

  const getClassroomPage = () => {
    return {
      component: (
        <Classroom
          studentGroup={props.studentGroup}
          inActiveSchoolYear={props.inActiveSchoolYear}
          isReadOnly={props.isReadOnly}
          schoolYear={props.schoolYear}
        />
      ),
      navName: navs.students
    };
  };

  const getClasswideInterventionPage = () => {
    const assessmentResult =
      props.currentAssessmentResults.find(ar => ar.type === "classwide") || props.classwideAssessmentResultForPrinting;
    return {
      component: !assessmentResult ? (
        <ClasswideDetail
          studentGroupId={props.studentGroupId}
          siteId={props.studentGroup.siteId}
          situation={props.currentSituation}
        />
      ) : (
        <DashboardClasswide
          studentGroup={props.studentGroup}
          inActiveSchoolYear={props.inActiveSchoolYear}
          isReadOnly={props.isReadOnly}
          students={props.students}
          assessmentResult={assessmentResult}
          isPrinting={props.isPrinting}
        />
      ),
      navName: navs.classwide
    };
  };

  const getScreeningPage = () => {
    return {
      component: (
        <Screening
          studentGroup={props.studentGroup}
          inActiveSchoolYear={props.inActiveSchoolYear}
          isReadOnly={props.isReadOnly}
          activeComponent={props.activeScreeningComponent}
          assessmentResultId={props.assessmentResultId}
          students={props.students}
          screeningBenchmarkPeriodId={props.screeningBenchmarkPeriodId}
        />
      ),
      navName: navs.screening
    };
  };

  const getInterventionWithoutScreening = () => {
    return {
      component: (
        <InterventionWithoutScreening
          studentGroup={props.studentGroup}
          inActiveSchoolYear={props.inActiveSchoolYear}
          isReadOnly={props.isReadOnly}
          activeComponent={props.activeScreeningComponent}
          assessmentResultId={props.assessmentResultId}
          students={props.students}
          screeningBenchmarkPeriodId={props.screeningBenchmarkPeriodId}
          startNewClasswide={startNewClasswide}
        />
      ),
      navName: navs.interventionWithoutScreening
    };
  };

  const getActiveViewForHighSchoolClass = () => {
    if (props.activeNavName === navs.students) {
      return getClassroomPage();
    }
    if (props.activeNavName === navs.individual) {
      return getIndividualInterventionPage();
    }

    const hasStartedClasswideIntervention = !!get(props, "studentGroup.currentClasswideSkill.benchmarkPeriodId");

    if (hasStartedClasswideIntervention) {
      switch (props.activeNavName) {
        case navs.growth:
          return getGrowthPage();
        case navs.classwide:
        default:
          return getClasswideInterventionPage();
      }
    } else if (!props.activeNavName || props.activeNavName === navs.screening) {
      return getScreeningPage();
    }

    return getClassroomPage();
  };

  const renderPrimaryComponent = () => {
    if (isEmpty(props.studentGroup)) {
      return null;
    }

    if (props.isPrinting && props.match?.params?.component === "ClasswideIntervention") {
      return getClasswideInterventionPage();
    }

    const isHighSchoolGroup = isHighSchoolGrade(props.studentGroup.grade);
    if (isHighSchoolGroup) {
      return getActiveViewForHighSchoolClass();
    }

    if (
      (props.currentSituation & (situations.screening | situations.needsScreening) && !props.activeNavName) ||
      props.activeNavName === navs.screening
    ) {
      return getScreeningPage();
    }
    if (
      props.currentSituation & situations.classwide &&
      (!props.activeNavName || props.activeNavName === navs.classwide)
    ) {
      return getClasswideInterventionPage();
    }
    if (
      props.currentSituation & situations.individual &&
      (!props.activeNavName || props.activeNavName === navs.individual)
    ) {
      return getIndividualInterventionPage();
    }
    if (props.activeNavName === navs.growth) {
      return getGrowthPage();
    }
    if (props.activeNavName === navs.interventionWithoutScreening) {
      return getInterventionWithoutScreening();
    }
    return getClassroomPage();
  };

  if (props.loading) {
    return <Loading />;
  }
  const { currentSituation, studentGroup, students } = props;
  const primaryRenderChoice = renderPrimaryComponent();
  if (!primaryRenderChoice) {
    return null;
  }
  if (context.activeNavName !== primaryRenderChoice.navName) {
    context.setContext({
      activeNavName: primaryRenderChoice.navName
    });
  }
  const idsOfStudentsInIndividualInterventions = props.currentAssessmentResults
    .filter(({ type }) => type === "individual")
    .map(({ studentId }) => studentId);
  studentGroup.individualInterventionQueue = (studentGroup.individualInterventionQueue || []).filter(studentId =>
    students.find(({ _id }) => _id === studentId)
  );

  return (
    <div className={`p-2 ${props.isPrinting ? "" : "main-content"}`}>
      <ScoreEntryProvider>
        {!props.isPrinting && (
          <React.Fragment>
            <MessageNotice noticeLocation="dashboard" />
            <DashboardNav
              situation={currentSituation}
              idsOfStudentsInIndividualInterventions={idsOfStudentsInIndividualInterventions}
              activeNav={primaryRenderChoice.navName}
              studentGroup={studentGroup}
            />
          </React.Fragment>
        )}
        {primaryRenderChoice.component}
      </ScoreEntryProvider>
    </div>
  );
}

Dashboard.propTypes = {
  activeNavName: PropTypes.string,
  activeScreeningComponent: PropTypes.string,
  assessmentResultId: PropTypes.string,
  currentAssessmentResults: PropTypes.array,
  currentSituation: PropTypes.number,
  inActiveSchoolYear: PropTypes.bool,
  loading: PropTypes.bool,
  studentGroup: PropTypes.object,
  user: PropTypes.object,
  studentGroupId: PropTypes.string,
  screeningBenchmarkPeriodId: PropTypes.string,
  students: PropTypes.array,
  isReadOnly: PropTypes.bool,
  isPrinting: PropTypes.bool,
  match: PropTypes.object,
  classwideAssessmentResultForPrinting: PropTypes.object,
  schoolYear: PropTypes.number
};

function getNextBenchmarkPeriodDate(benchmarkPeriodDateObject) {
  const { month, day } = benchmarkPeriodDateObject;
  const currentDate = moment();
  const year =
    currentDate.month() < month - 1 || (currentDate.month() === month - 1 && currentDate.day() <= day)
      ? currentDate.year()
      : currentDate.year() + 1;
  return moment({ year, month: month - 1, day });
}

function calculateCurrentSituation({ currentAssessmentResults, studentGroup, benchmarkPeriod, students, isPrinting }) {
  let currentSituation = 0;

  if (currentAssessmentResults && currentAssessmentResults.length) {
    if (currentAssessmentResults.some(ar => ar.type === "benchmark")) {
      currentSituation |= situations.screening;
    }
    if (currentAssessmentResults.some(ar => ar.type === "classwide")) {
      currentSituation |= situations.classwide;
    }
    if (currentAssessmentResults.some(ar => ar.type === "individual")) {
      currentSituation |= situations.individual;
    }
  }

  // Check if group needs screening
  const isMissingBenchmarkScore =
    !studentGroup.history ||
    !studentGroup.history.some(h => h.type === "benchmark" && h.benchmarkPeriodId === benchmarkPeriod._id);
  const isNotCurrentlyPracticingBenchmark =
    !currentAssessmentResults ||
    !currentAssessmentResults.some(ar => ar.type === "benchmark" && ar.benchmarkPeriodId === benchmarkPeriod._id);

  if (isMissingBenchmarkScore && isNotCurrentlyPracticingBenchmark && !isPrinting) {
    currentSituation |= situations.needsScreening;
  }

  // Check for completed classwide interventions that have undismissed messages
  if (get(studentGroup, "currentClasswideSkill.message.messageCode") === "5") {
    currentSituation |= situations.classwide;
  }

  // Check for completed individual interventions that have undismissed messages
  if (students.some(stu => stu.currentSkill && stu.currentSkill.message && !stu.currentSkill.message.dismissed)) {
    currentSituation |= situations.individual;
  }

  return currentSituation;
}

// eslint-disable-next-line spaced-comment
/** ****************************************************************
 // Data Container
 ***************************************************************** */
function DashboardContainer({ studentGroupId, activeNavName, siteId }) {
  const { benchmarkPeriods } = useContext(StaticDataContext);
  const { org } = useContext(OrganizationContext);
  const { customDate } = useContext(SchoolYearContext);
  const { studentGroup: contextStudentGroup, studentsInStudentGroup: students } = useContext(StudentGroupContext);
  const { benchmarkPeriodsGroupId, isTestOrg } = org || {};
  const history = useHistory();
  const studentGroup = contextStudentGroup || {};

  const { schoolYear: contextSchoolYear, latestAvailableSchoolYear } = useContext(SchoolYearContext);
  const { orgId: paramOrgid } = useContext(OrganizationContext);

  const [screeningWindowNotice, setScreeningWindowNotice] = useState({ show: false });

  // Handle async messageNotice operations
  useEffect(() => {
    const handleMessageNotices = async () => {
      if (screeningWindowNotice.show) {
        await messageNoticeUtils.addMessageNotice({
          noticeLocation: "side-nav-layout",
          component:
            screeningWindowNotice.componentName === "screening-window-notice" ? (
              <ScreeningWindowNotice
                screeningSeason={screeningWindowNotice.name}
                daysBeforeScreeningWindow={screeningWindowNotice.daysBeforeScreeningWindow}
                showThreeDayWarning={screeningWindowNotice.showThreeDayWarning}
              />
            ) : (
              <ScreeningWindowStarted
                screeningSeason={screeningWindowNotice.name}
                studentGroup={screeningWindowNotice.studentGroup}
                screeningContinues={screeningWindowNotice.screeningContinues}
              />
            )
        });
      } else if (screeningWindowNotice.shouldClear) {
        // clear em out... think of better way though.
        await messageNoticeUtils.clearLocationMessages("side-nav-layout");
      }
    };

    handleMessageNotices();
  }, [screeningWindowNotice]);

  const trackerData = useTracker(() => {
    if (!contextSchoolYear || !latestAvailableSchoolYear) {
      return { loading: true };
    }

    const isPrinting = isOnPrintPage();
    const currentUserId = getMeteorUserId();
    if (!currentUserId) {
      history.push("/login");
      return { loading: true };
    }
    const user = getMeteorUserSync();
    if (!user) {
      return { loading: true };
    }

    const orgid = paramOrgid || get(user, "profile.orgid");
    const schoolYear = contextSchoolYear;
    let newScreeningWindowNotice = { show: false };
    let daysBeforeScreeningWindow = 0;
    let currentAssessmentResults = [];
    let benchmarkPeriod;
    let currentSituation = 0;
    let inActiveSchoolYear = false;
    let isReadOnly = true;
    let loading = true;
    let classwideAssessmentResultForPrinting;

    // Subscriptions
    const orgSub = Meteor.subscribe("Organizations", orgid);

    if (orgSub.ready()) {
      // const studentGroupsSub = Meteor.subscribe("StudentGroupsAssociatedWithUser", schoolYear, siteId);
      const studentGroupEnrollmentsSub = Meteor.subscribe("StudentGroupEnrollmentsInStudentGroup", studentGroupId);
      const assessmentsSub = Meteor.subscribe("Assessments", studentGroupId);
      const bmwSub = Meteor.subscribe("BenchmarkWindowsForStudentGroupAndPeriod", studentGroupId);
      let lastClasswideAssessmentResultId;
      const studentGroupHistory = [...(studentGroup.additionalHistory || []), ...(studentGroup.history || [])];
      const assessmentResultIds = [...(studentGroup.currentAssessmentResultIds || [])];
      if (studentGroup.currentClasswideSkill?.message?.messageCode === "5") {
        lastClasswideAssessmentResultId = studentGroupHistory?.find(historyItem => historyItem.type === "classwide")
          ?.assessmentResultId;
        if (lastClasswideAssessmentResultId) {
          assessmentResultIds.push(lastClasswideAssessmentResultId);
        }
      }
      const assessmentResultsSub = Meteor.subscribe("AssessmentResults:FindByIds", assessmentResultIds);

      const areSubsLoading = areSubscriptionsLoading(
        studentGroupEnrollmentsSub,
        // studentGroupsSub,
        assessmentsSub,
        bmwSub,
        assessmentResultsSub
      );
      if (!areSubsLoading) {
        loading = false;
        currentAssessmentResults = AssessmentResults.find(
          {
            _id: { $in: studentGroup.currentAssessmentResultIds || [] }
          },
          { sort: { "created.on": -1 } }
        ).fetch();
        if (lastClasswideAssessmentResultId) {
          classwideAssessmentResultForPrinting = AssessmentResults.findOne({
            _id: lastClasswideAssessmentResultId
          });
        }
        benchmarkPeriod = getBenchmarkPeriodByDate({
          customDate,
          benchmarkPeriodsGroupId,
          isTestOrg,
          benchmarkPeriods
        });
        inActiveSchoolYear = !!studentGroup && studentGroup.schoolYear === latestAvailableSchoolYear;
        isReadOnly = !inActiveSchoolYear || isSupportUser();

        currentSituation = calculateCurrentSituation({
          currentAssessmentResults,
          studentGroup,
          benchmarkPeriod,
          students,
          isPrinting
        });

        const nextBMPeriod = getNextBMPeriod({
          customDate,
          benchmarkPeriodsGroupId,
          isTestOrg,
          benchmarkPeriods
        });
        const nextBMPeriodStartDate = getNextBenchmarkPeriodDate(
          get(nextBMPeriod.startDate, benchmarkPeriodsGroupId, nextBMPeriod.startDate.default)
        );

        daysBeforeScreeningWindow = nextBMPeriodStartDate.diff(moment(), "d");
        if (inActiveSchoolYear && !isReadOnly) {
          if (daysBeforeScreeningWindow > 0 && daysBeforeScreeningWindow <= 14) {
            newScreeningWindowNotice = {
              show: true,
              name: nextBMPeriod.name,
              componentName: "screening-window-notice",
              showThreeDayWarning: daysBeforeScreeningWindow <= 3,
              daysBeforeScreeningWindow,
              studentGroup
            };
          } else if (
            (currentSituation & situations.needsScreening ||
              (currentSituation & situations.screening &&
                currentAssessmentResults &&
                currentAssessmentResults.some(
                  ar => ar.type === "benchmark" && ar.benchmarkPeriodId === benchmarkPeriod._id
                ))) &&
            activeNavName !== navs.screening &&
            activeNavName
          ) {
            newScreeningWindowNotice = {
              show: true,
              name: benchmarkPeriod.name,
              componentName: "screening-window-started",
              screeningContinues:
                currentAssessmentResults &&
                currentAssessmentResults.some(
                  ar => ar.type === "benchmark" && ar.benchmarkPeriodId === benchmarkPeriod._id
                ),
              daysBeforeScreeningWindow,
              studentGroup
            };
          } else {
            newScreeningWindowNotice = {
              show: false,
              shouldClear: true
            };
          }
        } else {
          newScreeningWindowNotice = {
            show: false,
            shouldClear: true
          };
        }
      }
    }

    setScreeningWindowNotice(newScreeningWindowNotice);

    return {
      classwideAssessmentResultForPrinting,
      currentAssessmentResults,
      currentSituation,
      inActiveSchoolYear,
      isReadOnly,
      loading,
      studentGroup: studentGroup || {},
      studentGroupId,
      students,
      schoolYear,
      isPrinting,
      user
    };
  }, [contextSchoolYear, latestAvailableSchoolYear, studentGroupId, activeNavName, paramOrgid, siteId]);

  return <Dashboard {...trackerData} activeNavName={activeNavName} match={{ params: {} }} />;
}

DashboardContainer.propTypes = {
  studentGroupId: PropTypes.string.isRequired,
  activeNavName: PropTypes.string,
  siteId: PropTypes.string.isRequired
};

export default DashboardContainer;
