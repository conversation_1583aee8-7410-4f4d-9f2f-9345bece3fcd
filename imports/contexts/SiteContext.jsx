import React, { createContext, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { OrganizationContext } from "./OrganizationContext";
import { UserContext } from "./UserContext";
import { SchoolYearContext } from "./SchoolYearContext";
import { StudentGroups } from "../api/studentGroups/studentGroups";

export const SiteContext = createContext({
  siteId: null,
  siteName: "",
  site: null,
  studentGroupsInSite: [],
  userStudentGroups: [],
  isLoading: false
});

export const SiteContextProvider = ({ children, siteId }) => {
  const { currentSiteAccess, userId } = useContext(UserContext);
  const { sitesInOrg, orgId } = useContext(OrganizationContext);
  const { schoolYear } = useContext(SchoolYearContext);

  const [potentialSite, setPotentialSite] = useState(null);
  const [studentGroupsInSite, setStudentGroupsInSite] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const userStudentGroups = useTracker(() => {
    if (!userId || !schoolYear || !siteId || !orgId) {
      return [];
    }

    // TODO(fmazur) - replace this with use effect that filters based on user orgid, user siteIds or primary role? and schoolYear
    const subscription = Meteor.subscribe("StudentGroupsAssociatedWithUser", schoolYear, siteId, orgId);
    if (subscription.ready()) {
      return StudentGroups.find({}, { sort: { name: 1 } }).fetch();
    }
    return [];
  }, [userId, schoolYear, siteId, orgId]);

  useEffect(() => {
    if (sitesInOrg.length) {
      // TODO(fmazur) - use current schoolYear siteId of a user if available otherwise use context siteId
      const site = sitesInOrg.find(s => s._id === (siteId || currentSiteAccess?.siteId));
      setPotentialSite(site);
      setIsLoading(false);
    }
  }, [sitesInOrg, siteId]);

  useEffect(() => {
    // TODO(fmazur) - limit data maybe for usage for all roles
    // TODO(fmazur) - StudentGroupsAssociatedWithUser publication
    if (orgId && siteId && schoolYear) {
      Meteor.call("StudentGroups:getGroups", { orgId, siteId, schoolYear }, (err, result) => {
        if (!err) {
          setStudentGroupsInSite(result);
        }
      });
    }
  }, [orgId, siteId, schoolYear]);

  useEffect(() => {
    if (!userId) {
      setPotentialSite(null);
      setStudentGroupsInSite([]);
    }
  }, [userId]);

  return (
    <SiteContext.Provider
      value={{
        siteId: userId ? siteId || potentialSite?._id || null : null,
        siteName: potentialSite?.name || "",
        site: potentialSite || null,
        studentGroupsInSite: studentGroupsInSite || [],
        userStudentGroups: userStudentGroups || [],
        isLoading
      }}
    >
      {children}
    </SiteContext.Provider>
  );
};

SiteContextProvider.propTypes = {
  children: PropTypes.node,
  siteId: PropTypes.string
};
